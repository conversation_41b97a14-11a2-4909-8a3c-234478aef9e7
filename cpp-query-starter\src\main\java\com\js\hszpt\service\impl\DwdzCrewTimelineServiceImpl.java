package com.js.hszpt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewTimeline;
import com.js.hszpt.mapper.DwdzCrewTimelineMapper;
import com.js.hszpt.service.DwdzCrewTimelineService;
import com.js.hszpt.service.DwdzCrewDefectRecordService;
import com.js.hszpt.service.CrewBasicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 船员动态履历时间轴宽表Service实现类
 * 
 * <AUTHOR> Generation
 */
@Slf4j
@Service
public class DwdzCrewTimelineServiceImpl extends ServiceImpl<DwdzCrewTimelineMapper, DwdzCrewTimeline>
        implements DwdzCrewTimelineService {

    @Autowired
    private DwdzCrewDefectRecordService defectRecordService;

    @Autowired
    private CrewBasicInfoService crewBasicInfoService;

    // 航行类型数据
    private Map<String, String> serviceNaviCategoryMap = new HashMap<>();

    private Map<String, String> dynamicTypeMap = new HashMap<>();

    // 培训结果中英文映射
    private Map<String, String> trainingResultMap = new HashMap<>();

    @PostConstruct
    private void init() {
        serviceNaviCategoryMap = MapUtil.builder(new HashMap<String, String>())
                .put("IN_PORT_DOMESTIC", "境内在港航行")
                .put("OFF_PORT_DOMESTIC", "境内港外航行")
                .put("OVERSEAS", "境外航行")
                .build();
        dynamicTypeMap = MapUtil.builder(new HashMap<String, String>())
                .put("培训", "training")
                .put("考试", "examination")
                .put("证书申请", "certificate")
                .put("履职", "service")
                .put("体检", "medical")
                .put("出入境", "entryexit")
                .put("监管记录", "supervision")
                .build();

        // 初始化培训结果中英文映射
        trainingResultMap.put("qualified", "合格");
        trainingResultMap.put("unqualified", "不合格");
        trainingResultMap.put("pass", "通过");
        trainingResultMap.put("fail", "未通过");
        trainingResultMap.put("excellent", "优秀");
        trainingResultMap.put("good", "良好");
        trainingResultMap.put("fair", "一般");
        trainingResultMap.put("poor", "差");
    }

    @Override
    public List<DwdzCrewTimeline> getByCrewId(String crewId) {
        log.info("根据船员ID查询时间轴数据，船员ID：{}", crewId);
        return this.baseMapper.selectByCrewId(crewId);
    }

    @Override
    public List<DwdzCrewTimeline> getByCrewIdAndYear(String crewId, Integer year) {
        log.info("根据船员ID和年份查询时间轴数据，船员ID：{}，年份：{}", crewId, year);
        return this.baseMapper.selectByCrewIdAndYear(crewId, year);
    }

    @Override
    public List<DwdzCrewTimeline> getByCrewIdAndEventType(String crewId, String eventType) {
        log.info("根据船员ID和事件类型查询时间轴数据，船员ID：{}，事件类型：{}", crewId, eventType);
        return this.baseMapper.selectByCrewIdAndEventType(crewId, eventType);
    }

    @Override
    public List<DwdzCrewTimeline> getByCrewIdAndEventTypeList(String crewId, List<String> eventTypeList) {
        return this.baseMapper.getByCrewIdAndEventTypeList(crewId, eventTypeList);
    }

    @Override
    public List<DwdzCrewTimeline> getByCrewIdAndEventTypeWithConditions(String crewId, String eventType,
            String shipType, String dutyType) {
        log.info("根据船员ID、事件类型、船舶类型、职务类型查询时间轴数据，船员ID：{}，事件类型：{}，船舶类型：{}，职务类型：{}",
                crewId, eventType, shipType, dutyType);
        return this.baseMapper.selectByCrewIdAndEventTypeWithConditions(crewId, eventType, shipType, dutyType);
    }

    @Override
    public List<Map<String, Object>> getTimelineData(String crewId, Integer year, Integer page, Integer pageSize) {
        log.info("获取时间轴数据，船员ID：{}，年份：{}，页码：{}，每页数量：{}", crewId, year, page, pageSize);

        List<DwdzCrewTimeline> timelineList;
        if (year != null) {
            timelineList = getByCrewIdAndYear(crewId, year);
        } else {
            timelineList = getByCrewId(crewId);
        }

        // 按年份分组
        Map<Integer, List<DwdzCrewTimeline>> yearGroups = new HashMap<>();
        
        // 先处理非履职记录，按event_date分组
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"service".equals(timeline.getEventType()) && !"training".equals(timeline.getEventType())) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(timeline.getEventDate());
                int eventYear = cal.get(Calendar.YEAR);
                yearGroups.computeIfAbsent(eventYear, k -> new ArrayList<>()).add(timeline);
            }
        }
        
        // 处理履职记录，按业务逻辑分配到对应年份
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"service".equals(timeline.getEventType())) {
                continue;
            }
            
            // 获取履职开始和结束日期
            Date serviceStartDate = timeline.getServiceDate();
            if (serviceStartDate == null) {
                serviceStartDate = getEffectiveStartDate(timeline);
            }
            
            Date serviceEndDate = timeline.getServiceEndDate();
            if (serviceEndDate == null) {
                serviceEndDate = timeline.getEndDate();
            }
            
            if (serviceStartDate == null || serviceEndDate == null) {
                continue;
            }
            
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(serviceStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(serviceEndDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            // 检查是否为跨年度履职
            boolean isCrossYear = endYear > startYear;
            
            // 在开始年份添加履职记录
            DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
            BeanUtil.copyProperties(timeline, startTimeline);
            startTimeline.setServiceDate(serviceStartDate);
            startTimeline.setServiceEndDate(serviceEndDate);
            // 标记为开始记录
            startTimeline.setSubType("start");
            yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
            
            // 如果是跨年度，在结束年份也添加履职记录
            if (isCrossYear) {
                DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, endTimeline);
                endTimeline.setServiceDate(serviceStartDate);
                endTimeline.setServiceEndDate(serviceEndDate);
                // 标记为结束记录
                endTimeline.setSubType("end");
                yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
            }
        }
        
        // 处理培训记录，按业务逻辑分配到对应年份
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"training".equals(timeline.getEventType())) {
                continue;
            }
            
            // 获取培训开始和结束日期
            Date trainingStartDate = timeline.getTrainingStartDate();
            Date trainingCompletionDate = timeline.getTrainingCompletionDate();
            if (trainingCompletionDate == null) {
                trainingCompletionDate = timeline.getTrainingEndDate();
            }
            
            if (trainingStartDate == null || trainingCompletionDate == null) {
                continue;
            }
            
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(trainingStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(trainingCompletionDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            // 检查是否为跨年度培训
            boolean isCrossYear = endYear > startYear;
            
            // 在开始年份添加培训记录
            DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
            BeanUtil.copyProperties(timeline, startTimeline);
            startTimeline.setTrainingStartDate(trainingStartDate);
            startTimeline.setTrainingCompletionDate(trainingCompletionDate);
            // 标记为开始记录
            startTimeline.setSubType("start");
            yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
            
            // 如果是跨年度，在结束年份也添加培训记录
            if (isCrossYear) {
                DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, endTimeline);
                endTimeline.setTrainingStartDate(trainingStartDate);
                endTimeline.setTrainingCompletionDate(trainingCompletionDate);
                // 标记为结束记录
                endTimeline.setSubType("end");
                yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
            }
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<Integer, List<DwdzCrewTimeline>> entry : yearGroups.entrySet()) {
            Map<String, Object> yearData = new HashMap<>();
            yearData.put("year", entry.getKey());
            yearData.put("expanded", true);

            List<Map<String, Object>> entries = entry.getValue().stream()
                    .map(this::convertToTimelineEntry)
                    .collect(Collectors.toList());
            yearData.put("entries", entries);

            result.add(yearData);
        }

        // 按年份降序排序
        result.sort((a, b) -> Integer.compare((Integer) b.get("year"), (Integer) a.get("year")));

        return result;
    }

    @Override
    public Map<String, Object> getCrossValidationTimeline(String crewId, String dateStart, String dateEnd,
            String dutyType, String shipType, String dynamicType, String businessData) {
        log.info("获取履职交叉验证时间轴数据，船员ID：{}，开始日期：{}，结束日期：{}，职务类型：{}，船舶类型：{}，动态类型：{}，业务数据类型：{}",
                crewId, dateStart, dateEnd, dutyType, shipType, dynamicType, businessData);
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询船员基本信息
            Map<String, Object> sailor = getSailorBasicInfo(crewId);
            result.put("sailor", sailor);

            // 收集所有需要查询缺陷信息的检查编号
            Set<String> inspectNoSet = new HashSet<>();
            
            // 获取所有动态信息（非service类型）用于收集检查编号
            List<DwdzCrewTimeline> allDynamicRecords = StrUtil.equals(dynamicType,"履职") ?
                    new ArrayList<>(): getAllDynamicRecords(crewId, dynamicType);
            
            // 根据日期范围过滤动态记录
            if (StrUtil.isNotBlank(dateStart) || StrUtil.isNotBlank(dateEnd)) {
                allDynamicRecords = filterDynamicRecordsByDateRange(allDynamicRecords, dateStart, dateEnd);
            }
            
            // 收集检查编号
            for (DwdzCrewTimeline timeline : allDynamicRecords) {
                if ("supervision".equals(timeline.getEventType()) && StrUtil.isNotBlank(timeline.getSupervisionInspectNo())) {
                    inspectNoSet.add(timeline.getSupervisionInspectNo());
                }
            }

            // 批量查询缺陷信息
            Map<String, List<Map<String, Object>>> defectRecordsMap = new HashMap<>();
            if (!inspectNoSet.isEmpty()) {
                List<String> inspectNoList = new ArrayList<>(inspectNoSet);
                defectRecordsMap = defectRecordService.getDefectRecordsForDisplayBatch(inspectNoList);
                log.info("批量查询缺陷信息，检查编号数量：{}", inspectNoList.size());
            }

            // 获取履职时间轴数据（以履职周期为主线）
            List<Map<String, Object>> timelineData = getServiceTimelineWithDynamics(crewId, dateStart, dateEnd,
                    dutyType, shipType, dynamicType, businessData, defectRecordsMap);
            result.put("timelineData", timelineData);

            return result;
        } catch (Exception e) {
            log.error("获取履职交叉验证时间轴数据失败，船员ID：{}", crewId, e);
            throw new RuntimeException("获取履职交叉验证时间轴数据失败", e);
        }
    }

    /**
     * 获取履职时间轴数据（以履职周期为主线，叠加动态信息）
     */
    private List<Map<String, Object>> getServiceTimelineWithDynamics(String crewId, String dateStart, String dateEnd,
            String dutyType, String shipType, String dynamicType, String businessData, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        log.info("获取履职时间轴数据，船员ID：{}，开始日期：{}，结束日期：{}，职务类型：{}，船舶类型：{}", crewId, dateStart, dateEnd, dutyType, shipType);

        if (StrUtil.equals(shipType,"all")) {
            shipType = "";
        }

        if (StrUtil.equals(dutyType,"all")) {
            dutyType = "";
        }

        // 1. 获取所有履职记录（service类型），增加船舶类型和职务类型条件
        List<DwdzCrewTimeline> serviceRecords = getByCrewIdAndEventTypeWithConditions(crewId, "service", shipType,
                dutyType);

        // 2. 根据日期范围过滤履职记录
        if (StrUtil.isNotBlank(dateStart) || StrUtil.isNotBlank(dateEnd)) {
            serviceRecords = filterServiceRecordsByDateRange(serviceRecords, dateStart, dateEnd);
            log.info("日期过滤后履职记录数量：{}", serviceRecords.size());
        }

        // 3. 获取所有动态信息（非service类型）
        List<DwdzCrewTimeline> dynamicRecords = StrUtil.equals(dynamicType,"履职") ?
                new ArrayList<>(): getAllDynamicRecords(crewId, dynamicType);

        // 4. 根据日期范围过滤动态记录
        if (StrUtil.isNotBlank(dateStart) || StrUtil.isNotBlank(dateEnd)) {
            dynamicRecords = filterDynamicRecordsByDateRange(dynamicRecords, dateStart, dateEnd);
            log.info("日期过滤后动态记录数量：{}", dynamicRecords.size());
        }

        List<Map<String, Object>> timelineData = new ArrayList<>();

        for (DwdzCrewTimeline serviceRecord : serviceRecords) {
            // 构建履职周期信息
            Map<String, Object> servicePeriod = buildServicePeriod(serviceRecord);

            // 获取该履职周期内的动态信息
            List<Map<String, Object>> dynamics = getDynamicsInServicePeriod(serviceRecord, dynamicRecords, defectRecordsMap);
            servicePeriod.put("dynamics", dynamics);
            // 添加到时间轴数据中
            if (StrUtil.equalsAny(dynamicType,"all","履职") || CollUtil.isNotEmpty(dynamics)) {
                timelineData.add(servicePeriod);
            }
        }

        // 按履职开始时间排序（降序）
        timelineData.sort((a, b) -> {
            String startDateA = (String) a.get("startDate");
            String startDateB = (String) b.get("startDate");
            if (startDateA == null)
                return 1;
            if (startDateB == null)
                return -1;
            return startDateB.compareTo(startDateA);
        });

        return timelineData;
    }

    /**
     * 构建履职周期信息
     */
    private Map<String, Object> buildServicePeriod(DwdzCrewTimeline serviceRecord) {
        Map<String, Object> servicePeriod = new HashMap<>();

        // 履职基本信息
        servicePeriod.put("serviceId", serviceRecord.getTimelineId());
        servicePeriod.put("vesselName", serviceRecord.getServiceVesselName());
        servicePeriod.put("vesselType", serviceRecord.getServiceVesselType());
        servicePeriod.put("position", serviceRecord.getServicePosition());
        servicePeriod.put("companyName", serviceRecord.getServiceCompanyName());

        // 优化日期处理逻辑：当startDate为null时，尝试使用其他日期字段
        Date effectiveStartDate = serviceRecord.getStartDate();
        if (effectiveStartDate == null) {
            if (serviceRecord.getServiceDate() != null) {
                effectiveStartDate = serviceRecord.getServiceDate();
            } else if (serviceRecord.getServiceAppointmentDate() != null) {
                effectiveStartDate = serviceRecord.getServiceAppointmentDate();
            } else if (serviceRecord.getEventDate() != null) {
                effectiveStartDate = serviceRecord.getEventDate();
            }
        }

        servicePeriod.put("startDate",
                effectiveStartDate != null ? new SimpleDateFormat("yyyy-MM-dd").format(effectiveStartDate) : null);
        Date normalizedEnd = normalizeEndDate(serviceRecord.getEndDate());
        servicePeriod.put("endDate", formatEndDateUntilNow(normalizedEnd));

        // 港口信息
        servicePeriod.put("startPort", serviceRecord.getServiceLocation());
        servicePeriod.put("endPort", serviceRecord.getServiceEndLocation());

        // 船舶信息
        servicePeriod.put("grossTonnage", serviceRecord.getServiceShipTonnage());
        // 从字典表找对应的值翻译
        String value = serviceNaviCategoryMap.get(serviceRecord.getServiceNaviCategory());
        String serviceNaviCategory = StrUtil.isEmpty(value) ? serviceRecord.getServiceNaviCategory() : value;
        servicePeriod.put("shipCategory", serviceNaviCategory);
        
        // 添加缺失的船舶相关字段
        servicePeriod.put("vesselNationality", serviceRecord.getServiceVesselNationality()); // 国籍
        servicePeriod.put("vesselRegno", serviceRecord.getServiceVesselRegno()); // 初次登记号
        servicePeriod.put("vesselImo", serviceRecord.getServiceVesselImo()); // IMO编号
        servicePeriod.put("vesselId", serviceRecord.getServiceVesselId()); // 船舶识别号
        servicePeriod.put("vesselNameEn", serviceRecord.getServiceVesselNameEn()); // 任职英文船名
        servicePeriod.put("vesselLevel", serviceRecord.getServiceVesselLevel()); // 船舶等级
        servicePeriod.put("shipEnginePower", serviceRecord.getServiceShipEnginePower()); // 船舶功率
        servicePeriod.put("location", serviceRecord.getServiceLocation()); // 任职地点
        servicePeriod.put("endLocation", serviceRecord.getServiceEndLocation()); // 解职地点
        // 航区信息
        String serviceVoyageDirection = serviceRecord.getServiceVoyageDirection();
        if (StrUtil.equals(serviceVoyageDirection,"境内航行")) {
            servicePeriod.put("voyageDirection", "沿海航区");
        }

        if (StrUtil.equals(serviceVoyageDirection,"国际航行")) {
            servicePeriod.put("voyageDirection", "无限航区");
        }

        // 派遣报备，展示：航运公司、外派公司；--展示两个公司名称
        // 中国籍船舶国际航行报备：服务机构、航运公司；--展示两个公司名称
        if (StrUtil.equalsAny(serviceRecord.getServiceDataSource(),"派遣报备","中国籍船舶国际航行报备")) {
            servicePeriod.put("companyName", serviceRecord.getServiceCompanyName());
            servicePeriod.put("dspCompanyName", serviceRecord.getServiceDspCompanyName());
        }

        // 状态信息
        servicePeriod.put("status", isOngoing(normalizedEnd) ? "current" : "completed");
        servicePeriod.put("dataSource", serviceRecord.getServiceDataSource());

        return servicePeriod;
    }

    /**
     * 获取所有动态记录（非service类型）
     */
    private List<DwdzCrewTimeline> getAllDynamicRecords(String crewId, String dynamicType) {
        List<DwdzCrewTimeline> allDynamics = new ArrayList<>();
        List<String> eventTypes = new ArrayList<>();
        // 修正：dynamicType为null、空字符串或"all"时，查所有动态类型
        if (dynamicType != null && !dynamicType.isEmpty() && !"all".equalsIgnoreCase(dynamicType)) {
            String value = dynamicTypeMap.get(dynamicType);
            eventTypes.add(value != null ? value : dynamicType);
        } else {
            // dynamicType为null、空字符串或"all"时，查所有动态类型（去掉行业转型transition）
            eventTypes = Arrays.asList("training", "examination", "certificate", "medical", "entryexit", "supervision");
        }
        List<DwdzCrewTimeline> dynamics = getByCrewIdAndEventTypeList(crewId, eventTypes);
        allDynamics.addAll(dynamics);
//        for (String eventType : eventTypes) {
//            List<DwdzCrewTimeline> dynamics = getByCrewIdAndEventType(crewId, eventType);
//            allDynamics.addAll(dynamics);
//        }
        return allDynamics;
    }

    // 建立按“有效日期”的动态索引（升序）
    private NavigableMap<Date, List<DwdzCrewTimeline>> buildDynamicsDateIndex(List<DwdzCrewTimeline> dynamics) {
        NavigableMap<Date, List<DwdzCrewTimeline>> index = new TreeMap<>();
        for (DwdzCrewTimeline dynamic : dynamics) {
            Date effective = getDynamicEffectiveDate(dynamic);
            if (effective == null) {
                continue;
            }
            index.computeIfAbsent(effective, k -> new ArrayList<>()).add(dynamic);
        }
        return index;
    }

    // 使用索引在 O(logN + K) 时间内获取履职区间内的动态，并转换、排序（按日期降序）
    private List<Map<String, Object>> getDynamicsInServicePeriodIndexed(
            DwdzCrewTimeline serviceRecord,
            NavigableMap<Date, List<DwdzCrewTimeline>> dynamicsIndex,
            Map<String, List<Map<String, Object>>> defectRecordsMap) {

        List<Map<String, Object>> dynamics = new ArrayList<>();

        Date serviceStartDate = getEffectiveStartDate(serviceRecord);
        Date serviceEndDate = normalizeEndDate(serviceRecord.getEndDate());

        if (serviceStartDate == null) {
            log.warn("船员{}的履职记录{}缺少开始日期信息", serviceRecord.getCrewId(), serviceRecord.getTimelineId());
            return dynamics;
        }
        // 进行中的履职：上界取“现在”，与原语义一致
        Date upper = serviceEndDate != null ? serviceEndDate : new Date();

        // 取子区间，遍历并转换
        for (List<DwdzCrewTimeline> sameDay : dynamicsIndex.subMap(serviceStartDate, true, upper, true).values()) {
            for (DwdzCrewTimeline dynamic : sameDay) {
                // 保险起见再做一次区间校验，确保边界一致
                if (isDynamicInServicePeriod(dynamic, serviceStartDate, serviceEndDate)) {
                    dynamics.add(convertDynamicToInfo(dynamic, defectRecordsMap));
                }
            }
        }

        // 按 date 字段降序
        dynamics.sort((a, b) -> {
            String da = (String) a.get("date");
            String db = (String) b.get("date");
            if (da == null) return 1;
            if (db == null) return -1;
            return db.compareTo(da);
        });

        return dynamics;
    }

    /**
     * 获取指定履职周期内的动态信息
     */
    private List<Map<String, Object>> getDynamicsInServicePeriod(DwdzCrewTimeline serviceRecord,
            List<DwdzCrewTimeline> allDynamics, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        List<Map<String, Object>> dynamics = new ArrayList<>();

        // 获取履职周期的起止时间
        Date serviceStartDate = getEffectiveStartDate(serviceRecord);
        Date serviceEndDate = normalizeEndDate(serviceRecord.getEndDate());

        if (serviceStartDate == null) {
            log.warn("船员{}的履职记录{}缺少开始日期信息", serviceRecord.getCrewId(), serviceRecord.getTimelineId());
            return dynamics;
        }

        for (DwdzCrewTimeline dynamic : allDynamics) {
            Date dynamicDate = getDynamicEffectiveDate(dynamic);
            if (isDynamicInServicePeriod(dynamic, serviceStartDate, serviceEndDate)) {
                Map<String, Object> dynamicInfo = convertDynamicToInfo(dynamic, defectRecordsMap);
                dynamics.add(dynamicInfo);
            }
        }

        // 按日期排序（降序）
        dynamics.sort((a, b) -> {
            String dateA = (String) a.get("date");
            String dateB = (String) b.get("date");
            if (dateA == null)
                return 1;
            if (dateB == null)
                return -1;
            return dateB.compareTo(dateA);
        });

        return dynamics;
    }

    /**
     * 获取履职记录的有效开始日期
     */
    private Date getEffectiveStartDate(DwdzCrewTimeline serviceRecord) {
        if (serviceRecord.getStartDate() != null) {
            return serviceRecord.getStartDate();
        }
        if (serviceRecord.getServiceDate() != null) {
            return serviceRecord.getServiceDate();
        }
        if (serviceRecord.getServiceAppointmentDate() != null) {
            return serviceRecord.getServiceAppointmentDate();
        }
        if (serviceRecord.getEventDate() != null) {
            return serviceRecord.getEventDate();
        }
        return null;
    }

    /**
     * 判断动态是否在履职周期内
     */
    private boolean isDynamicInServicePeriod(DwdzCrewTimeline dynamic, Date serviceStartDate, Date serviceEndDate) {
        Date dynamicDate = getDynamicEffectiveDate(dynamic);
        if (dynamicDate == null) {
            return false;
        }

        // 动态日期在履职开始日期之后
        if (dynamicDate.before(serviceStartDate)) {
            return false;
        }

        // 如果履职有结束日期，动态日期在履职结束日期之前
        if (serviceEndDate != null && dynamicDate.after(serviceEndDate)) {
            return false;
        }

        return true;
    }

    /**
     * 获取动态记录的有效日期
     */
    private Date getDynamicEffectiveDate(DwdzCrewTimeline dynamic) {
        switch (dynamic.getEventType()) {
            case "training":
                return dynamic.getTrainingStartDate() != null ? dynamic.getTrainingStartDate() : dynamic.getEventDate();
            case "examination":
                return dynamic.getExamExamDate() != null ? dynamic.getExamExamDate()
                        : (dynamic.getExamRegistrationDate() != null ? dynamic.getExamRegistrationDate()
                                : dynamic.getEventDate());
            case "certificate":
                return dynamic.getCertApplyApprovalDate() != null ? dynamic.getCertApplyApprovalDate()
                        : dynamic.getEventDate();
            case "medical":
                return dynamic.getMedicalExamDate() != null ? dynamic.getMedicalExamDate() : dynamic.getEventDate();
            case "entryexit":
                return dynamic.getImmigrationEntryExitTime() != null ? dynamic.getImmigrationEntryExitTime()
                        : dynamic.getEventDate();
            case "supervision":
                return dynamic.getSupervisionProcessingDate() != null ? dynamic.getSupervisionProcessingDate()
                        : (dynamic.getSupervisionIncidentDate() != null ? dynamic.getSupervisionIncidentDate()
                                : dynamic.getEventDate());
            case "transition":
                return dynamic.getCareerChangeEffectiveDate() != null ? dynamic.getCareerChangeEffectiveDate()
                        : dynamic.getEventDate();
            default:
                return dynamic.getEventDate();
        }
    }

    /**
     * 将动态记录转换为信息格式
     */
    private Map<String, Object> convertDynamicToInfo(DwdzCrewTimeline dynamic, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        Map<String, Object> dynamicInfo = new HashMap<>();
        dynamicInfo.put("id", dynamic.getTimelineId());
        dynamicInfo.put("type", dynamic.getEventType());
        dynamicInfo.put("date", new SimpleDateFormat("yyyy-MM-dd").format(getDynamicEffectiveDate(dynamic)));
        dynamicInfo.put("title", dynamic.getEventTitle());
        dynamicInfo.put("status", dynamic.getEventStatus());

        // 根据事件类型添加特定字段
        switch (dynamic.getEventType()) {
            case "training":
                convertTrainingDynamic(dynamicInfo, dynamic);
                break;
            case "examination":
                convertExaminationDynamic(dynamicInfo, dynamic);
                break;
            case "certificate":
                convertCertificateDynamic(dynamicInfo, dynamic);
                break;
            case "medical":
                convertMedicalDynamic(dynamicInfo, dynamic);
                break;
            case "entryexit":
                convertEntryExitDynamic(dynamicInfo, dynamic);
                break;
            case "supervision":
                convertSupervisionDynamic(dynamicInfo, dynamic, defectRecordsMap);
                break;
            // 去掉行业转型
        }

        return dynamicInfo;
    }

    /**
     * 转换培训动态
     */
    private void convertTrainingDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        dynamicInfo.put("trainingProject", dynamic.getTrainingProject());
        dynamicInfo.put("trainingInstitution", dynamic.getTrainingInstitution());
        dynamicInfo.put("trainingClassName", dynamic.getTrainingClassName());
        dynamicInfo.put("startDate",
                dynamic.getTrainingStartDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getTrainingStartDate())
                        : null);
        dynamicInfo.put("completionDate",
                dynamic.getTrainingCompletionDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getTrainingCompletionDate())
                        : null);
        // 翻译培训结果
        dynamicInfo.put("trainingResult", translateTrainingResult(dynamic.getTrainingResult()));
        dynamicInfo.put("description", dynamic.getTrainingDetails());
    }

    /**
     * 转换考试动态
     */
    private void convertExaminationDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        if (dynamic.getExamRegistrationDate() != null) {
            // 考试报名
            dynamicInfo.put("subType", "registration");
            dynamicInfo.put("marineAuthority", dynamic.getExamMarineAuthority());
            dynamicInfo.put("examLocation", dynamic.getExamLocation());
            dynamicInfo.put("examPeriod", dynamic.getExamPeriod());
            dynamicInfo.put("examSession", dynamic.getExamSession());
            dynamicInfo.put("position", dynamic.getExamPosition());
            dynamicInfo.put("examSubjects", dynamic.getExamSubjects());
            dynamicInfo.put("registrationDate",
                    new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getExamRegistrationDate()));
            dynamicInfo.put("registrationResult", dynamic.getExamRegistrationResult());
        } else {
            // 考试
            dynamicInfo.put("subType", "exam");
            dynamicInfo.put("examLocation", dynamic.getExamLocation());
            dynamicInfo.put("marineAuthority", dynamic.getExamMarineAuthority());
            dynamicInfo.put("examPeriod", dynamic.getExamPeriod());
            dynamicInfo.put("position", dynamic.getExamPosition());
            dynamicInfo.put("examSubjects", dynamic.getExamSubjects());
            dynamicInfo.put("examDate",
                    dynamic.getExamExamDate() != null
                            ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getExamExamDate())
                            : null);
            dynamicInfo.put("examResult", dynamic.getExamResult());
        }
        dynamicInfo.put("description", dynamic.getExamDetails());
    }

    /**
     * 转换证书申请动态
     */
    private void convertCertificateDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        String certificateName = dynamic.getCertApplyCertificateName();
        if (certificateName != null && certificateName.contains("适任证书")) {
            dynamicInfo.put("subType", "competency");
        } else {
            dynamicInfo.put("subType", "training");
        }

        dynamicInfo.put("certificateName", certificateName);
        dynamicInfo.put("certificateNumber", dynamic.getCertApplyCertificateNumber());
        dynamicInfo.put("applicationType", dynamic.getCertApplyApplicationType());
        dynamicInfo.put("applicationResult", dynamic.getCertApplyApplicationResult());
        dynamicInfo.put("approvalDate",
                dynamic.getCertApplyApprovalDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getCertApplyApprovalDate())
                        : null);
        dynamicInfo.put("marineAuthority", dynamic.getCertApplyMarineAuthority());
        dynamicInfo.put("description", dynamic.getCertApplyDetails());
    }

    /**
     * 转换体检动态
     */
    private void convertMedicalDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        dynamicInfo.put("medicalHospital", dynamic.getMedicalHospital());
        dynamicInfo.put("applicableDepartment", dynamic.getMedicalDepartment());
        dynamicInfo.put("medicalDate",
                dynamic.getMedicalExamDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getMedicalExamDate())
                        : null);
        dynamicInfo.put("medicalResult", dynamic.getMedicalResult());
        dynamicInfo.put("description", dynamic.getMedicalDetails());
    }

    /**
     * 转换出入境动态
     */
    private void convertEntryExitDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        dynamicInfo.put("direction", dynamic.getImmigrationDirection());
        dynamicInfo.put("destinationCountry", dynamic.getImmigrationDestinationCountry());
        dynamicInfo.put("borderControl", dynamic.getImmigrationBorderControl());
        dynamicInfo.put("port", dynamic.getImmigrationPort());
        dynamicInfo.put("entryExitTime",
                dynamic.getImmigrationEntryExitTime() != null
                        ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dynamic.getImmigrationEntryExitTime())
                        : null);
        dynamicInfo.put("description", dynamic.getImmigrationDetails());
    }

    /**
     * 转换监管记录动态
     */
    private void convertSupervisionDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        String subType = dynamic.getSubType();
        dynamicInfo.put("subType", subType);

        switch (subType) {
            case "award":
                // 奖励
                dynamicInfo.put("awardAuthority", dynamic.getSupervisionAuthority());
                dynamicInfo.put("awardReason", dynamic.getSupervisionAwardReason());
                dynamicInfo.put("awardContent", dynamic.getSupervisionAwardContent());
                dynamicInfo.put("awardDate",
                        dynamic.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getSupervisionProcessingDate())
                                : null);
                break;
            case "violation":
                // 违法记分
                dynamicInfo.put("scoringAuthority", dynamic.getSupervisionAuthority());
                dynamicInfo.put("scorePoints", dynamic.getSupervisionScorePoints());
                dynamicInfo.put("scoringDate",
                        dynamic.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getSupervisionProcessingDate())
                                : null);
                break;
            case "penalty":
                // 行政处罚
                dynamicInfo.put("caseNumber", dynamic.getSupervisionCaseNumber());
                dynamicInfo.put("incidentDate",
                        dynamic.getSupervisionIncidentDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getSupervisionIncidentDate())
                                : null);
                dynamicInfo.put("incidentLocation", dynamic.getSupervisionIncidentLocation());
                dynamicInfo.put("violationLocation", dynamic.getSupervisionViolationLocation());
                dynamicInfo.put("penaltyDecision", dynamic.getSupervisionPenaltyDecision());
                dynamicInfo.put("certificateDetained", dynamic.getSupervisionCertificateDetain());
                break;
            case "inspection":
            case "fsc":
            case "psc":
                // 安全检查
                dynamicInfo.put("inspectionAuthority", dynamic.getSupervisionAuthority());
                dynamicInfo.put("vesselName", dynamic.getSupervisionVesselName());
                dynamicInfo.put("inspectionDate",
                        dynamic.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getSupervisionProcessingDate())
                                : null);
                
                // 从批量查询结果中获取缺陷详情
                List<Map<String, Object>> defectRecords = new ArrayList<>();
                if (StrUtil.isNotBlank(dynamic.getSupervisionInspectNo())) {
                    defectRecords = defectRecordsMap.get(dynamic.getSupervisionInspectNo());
                    if (defectRecords == null) {
                        defectRecords = new ArrayList<>();
                    }
                }
                dynamicInfo.put("defectRecords", defectRecords);
                
                // 根据缺陷记录数量设置defectDetails
                if (defectRecords.isEmpty()) {
                    dynamicInfo.put("defectDetails", "零缺陷");
                } else {
                    dynamicInfo.put("defectDetails", String.valueOf(defectRecords.size()));
                }
                
                // 添加初查/复查显示
                String initCodeDynamic = dynamic.getSupervisionInitCode();
                if (StrUtil.isNotBlank(initCodeDynamic)) {
                    if ("0".equals(initCodeDynamic)) {
                        dynamicInfo.put("inspectionType", "初查");
                    } else if ("1".equals(initCodeDynamic)) {
                        dynamicInfo.put("inspectionType", "复查");
                    } else {
                        dynamicInfo.put("inspectionType", "监管");
                    }
                } else {
                    dynamicInfo.put("inspectionType", "监管");
                }
                break;
            default:
                // 默认安全检查
                dynamicInfo.put("inspectionAuthority", dynamic.getSupervisionAuthority());
                dynamicInfo.put("vesselName", dynamic.getSupervisionVesselName());
                dynamicInfo.put("inspectionDate",
                        dynamic.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getSupervisionProcessingDate())
                                : null);
                
                // 从批量查询结果中获取缺陷详情
                List<Map<String, Object>> defectRecordsDefault = new ArrayList<>();
                if (StrUtil.isNotBlank(dynamic.getSupervisionInspectNo())) {
                    defectRecordsDefault = defectRecordsMap.get(dynamic.getSupervisionInspectNo());
                    if (defectRecordsDefault == null) {
                        defectRecordsDefault = new ArrayList<>();
                    }
                }
                dynamicInfo.put("defectRecords", defectRecordsDefault);
                
                // 根据缺陷记录数量设置defectDetails
                if (defectRecordsDefault.isEmpty()) {
                    dynamicInfo.put("defectDetails", "零缺陷");
                } else {
                    dynamicInfo.put("defectDetails", String.valueOf(defectRecordsDefault.size()));
                }
                
                // 添加初查/复查显示
                String initCodeDefaultDynamic = dynamic.getSupervisionInitCode();
                if (StrUtil.isNotBlank(initCodeDefaultDynamic)) {
                    if ("0".equals(initCodeDefaultDynamic)) {
                        dynamicInfo.put("inspectionType", "初查");
                    } else if ("1".equals(initCodeDefaultDynamic)) {
                        dynamicInfo.put("inspectionType", "复查");
                    } else {
                        dynamicInfo.put("inspectionType", "监管");
                    }
                } else {
                    dynamicInfo.put("inspectionType", "监管");
                }
                break;
        }
        dynamicInfo.put("description", dynamic.getSupervisionDetails());
    }

    /**
     * 转换行业转型动态
     */
    private void convertTransitionDynamic(Map<String, Object> dynamicInfo, DwdzCrewTimeline dynamic) {
        dynamicInfo.put("company", dynamic.getCareerChangeCompany());
        dynamicInfo.put("transitionIdentity", dynamic.getCareerChangeNewIdentity());
        dynamicInfo.put("transitionDate",
                dynamic.getCareerChangeEffectiveDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(dynamic.getCareerChangeEffectiveDate())
                        : null);
        dynamicInfo.put("description", dynamic.getCareerChangeDetails());
    }

    /**
     * 获取船员基本信息
     */
    private Map<String, Object> getSailorBasicInfo(String crewId) {
        // 这里应该从船员基本信息表查询，暂时返回模拟数据
        Map<String, Object> sailor = new HashMap<>();
        sailor.put("name", "张三");
        sailor.put("idNumber", "110101199001011234");
        sailor.put("nationality", "中国");
        sailor.put("currentAge", 34);
        return sailor;
    }

    /**
     * 将时间轴实体转换为前端需要的格式
     */
    private Map<String, Object> convertToTimelineEntry(DwdzCrewTimeline timeline) {
        Map<String, Object> entry = new HashMap<>();
        entry.put("id", timeline.getTimelineId());
        entry.put("type", timeline.getEventType());
        entry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(timeline.getEventDate()));
        entry.put("title", timeline.getEventTitle());
        entry.put("status", timeline.getEventStatus());

        // 根据事件类型添加特定字段
        switch (timeline.getEventType()) {
            case "training":
                entry.put("trainingProject", timeline.getTrainingProject());
                entry.put("trainingInstitution", timeline.getTrainingInstitution());
                entry.put("trainingClassName", timeline.getTrainingClassName());
                entry.put("startDate", timeline.getTrainingStartDate());
                entry.put("completionDate", timeline.getTrainingCompletionDate());
                entry.put("trainingResult", timeline.getTrainingResult());
                break;
            case "service":
                entry.put("vesselName", timeline.getServiceVesselName());
                entry.put("position", timeline.getServicePosition());
                entry.put("companyName", timeline.getServiceCompanyName());
                entry.put("vesselType", timeline.getServiceVesselType());
                entry.put("appointmentDate", timeline.getServiceAppointmentDate());
                entry.put("serviceLocation", timeline.getServiceLocation());
                entry.put("dataSource", timeline.getServiceDataSource());
                break;
            case "supervision":
                entry.put("subType", timeline.getSubType());
                entry.put("inspectionAuthority", timeline.getSupervisionAuthority());
                entry.put("vesselName", timeline.getSupervisionVesselName());
                entry.put("inspectionDate", timeline.getSupervisionProcessingDate());
                entry.put("defectDetails", timeline.getSupervisionDefectDetails());
                break;
            default:
                // 其他事件类型的处理
                break;
        }

        return entry;
    }

    @Override
    public List<Map<String, Object>> getTimelineDataV1(String crewId, Integer year) {
        log.info("获取船员履历时间轴数据V1，船员ID：{}，年份：{}", crewId, year);

        // 获取船员基本信息，用于计算年龄
        Map<String, Object> crewBasicInfo = crewBasicInfoService.getSeafarerBasicInfo(crewId);
        Date birthDate = null;
        if (crewBasicInfo != null && crewBasicInfo.get("birthDate") != null) {
            birthDate = (Date) crewBasicInfo.get("birthDate");
        }

        List<DwdzCrewTimeline> timelineList;
        if (year != null) {
            timelineList = getByCrewIdAndYear(crewId, year);
        } else {
            timelineList = getByCrewId(crewId);
        }

        HashMap<String, DwdzCrewTimeline> map = timelineList.stream()
                .collect(Collectors.toMap(
                        DwdzCrewTimeline::getTimelineId, Function.identity(),
                        (k1, k2) -> k1,
                        HashMap::new));

        // 收集所有需要查询缺陷信息的检查编号
        Set<String> inspectNoSet = new HashSet<>();
        for (DwdzCrewTimeline timeline : timelineList) {
            if ("supervision".equals(timeline.getEventType()) && StrUtil.isNotBlank(timeline.getSupervisionInspectNo())) {
                inspectNoSet.add(timeline.getSupervisionInspectNo());
            }
        }

        // 批量查询缺陷信息
        Map<String, List<Map<String, Object>>> defectRecordsMap = new HashMap<>();
        if (!inspectNoSet.isEmpty()) {
            List<String> inspectNoList = new ArrayList<>(inspectNoSet);
            defectRecordsMap = defectRecordService.getDefectRecordsForDisplayBatch(inspectNoList);
            log.info("批量查询缺陷信息，检查编号数量：{}", inspectNoList.size());
        }

        // 按年份分组
        Map<Integer, List<DwdzCrewTimeline>> yearGroups = new HashMap<>();
        
        // 先处理非履职记录，按event_date分组
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"service".equals(timeline.getEventType()) && !"training".equals(timeline.getEventType())) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(timeline.getEventDate());
                int eventYear = cal.get(Calendar.YEAR);
                
                // 如果指定了年份，只处理该年份的记录
                if (year == null || eventYear == year) {
                    yearGroups.computeIfAbsent(eventYear, k -> new ArrayList<>()).add(timeline);
                }
            }
        }
        
        // 处理履职记录，按业务逻辑分配到对应年份
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"service".equals(timeline.getEventType())) {
                continue;
            }
            
            // 获取履职开始和结束日期
            Date serviceStartDate = timeline.getServiceDate();
            if (serviceStartDate == null) {
                serviceStartDate = getEffectiveStartDate(timeline);
            }
            
            Date serviceEndDate = timeline.getServiceEndDate();
            if (serviceEndDate == null) {
                serviceEndDate = timeline.getEndDate();
            }
            
            if (serviceStartDate == null || serviceEndDate == null) {
                continue;
            }
            
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(serviceStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(serviceEndDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            // 检查是否为跨年度履职
            boolean isCrossYear = endYear > startYear;
            
            // 如果指定了年份，只处理与该年份相关的记录
            if (year != null) {
                if (startYear == year) {
                    // 在开始年份添加履职记录
                    DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
                    BeanUtil.copyProperties(timeline, startTimeline);
                    startTimeline.setServiceDate(serviceStartDate);
                    startTimeline.setServiceEndDate(serviceEndDate);
                    // 标记为开始记录
                    startTimeline.setSubType("start");
                    yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
                }
                if (endYear == year) {
                    // 在结束年份添加履职记录（不管是否跨年）
                    DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                    BeanUtil.copyProperties(timeline, endTimeline);
                    endTimeline.setServiceDate(serviceStartDate);
                    endTimeline.setServiceEndDate(serviceEndDate);
                    // 标记为结束记录
                    endTimeline.setSubType("end");
                    yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
                }
            } else {
                // 如果没有指定年份，处理所有记录
                // 在开始年份添加履职记录
                DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, startTimeline);
                startTimeline.setServiceDate(serviceStartDate);
                startTimeline.setServiceEndDate(serviceEndDate);
                // 标记为开始记录
                startTimeline.setSubType("start");
                yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
                
                // 在结束年份也添加履职记录（不管是否跨年）
                DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, endTimeline);
                endTimeline.setServiceDate(serviceStartDate);
                endTimeline.setServiceEndDate(serviceEndDate);
                // 标记为结束记录
                endTimeline.setSubType("end");
                yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
            }
        }
        
        // 处理培训记录，按业务逻辑分配到对应年份
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"training".equals(timeline.getEventType())) {
                continue;
            }
            
            // 获取培训开始和结束日期
            Date trainingStartDate = timeline.getTrainingStartDate();
            Date trainingCompletionDate = timeline.getTrainingCompletionDate();
            if (trainingCompletionDate == null) {
                trainingCompletionDate = timeline.getTrainingEndDate();
            }
            
            if (trainingStartDate == null || trainingCompletionDate == null) {
                continue;
            }
            
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(trainingStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(trainingCompletionDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            // 检查是否为跨年度培训
            boolean isCrossYear = endYear > startYear;
            
            // 如果指定了年份，只处理与该年份相关的记录
            if (year != null) {
                if (startYear == year) {
                    // 在开始年份添加培训记录
                    DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
                    BeanUtil.copyProperties(timeline, startTimeline);
                    startTimeline.setTrainingStartDate(trainingStartDate);
                    startTimeline.setTrainingCompletionDate(trainingCompletionDate);
                    // 标记为开始记录
                    startTimeline.setSubType("start");
                    yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
                }
                if (endYear == year) {
                    // 在结束年份添加培训记录（不管是否跨年）
                    DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                    BeanUtil.copyProperties(timeline, endTimeline);
                    endTimeline.setTrainingStartDate(trainingStartDate);
                    endTimeline.setTrainingCompletionDate(trainingCompletionDate);
                    // 标记为结束记录
                    endTimeline.setSubType("end");
                    yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
                }
            } else {
                // 如果没有指定年份，处理所有记录
                // 在开始年份添加培训记录
                DwdzCrewTimeline startTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, startTimeline);
                startTimeline.setTrainingStartDate(trainingStartDate);
                startTimeline.setTrainingCompletionDate(trainingCompletionDate);
                // 标记为开始记录
                startTimeline.setSubType("start");
                yearGroups.computeIfAbsent(startYear, k -> new ArrayList<>()).add(startTimeline);
                
                // 在结束年份也添加培训记录（不管是否跨年）
                DwdzCrewTimeline endTimeline = new DwdzCrewTimeline();
                BeanUtil.copyProperties(timeline, endTimeline);
                endTimeline.setTrainingStartDate(trainingStartDate);
                endTimeline.setTrainingCompletionDate(trainingCompletionDate);
                // 标记为结束记录
                endTimeline.setSubType("end");
                yearGroups.computeIfAbsent(endYear, k -> new ArrayList<>()).add(endTimeline);
            }
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<Integer, List<DwdzCrewTimeline>> entry : yearGroups.entrySet()) {
            Map<String, Object> yearData = new HashMap<>();
            int currentYear = entry.getKey();
            yearData.put("year", String.valueOf(currentYear));
            
            // 计算该年份的年龄
            if (birthDate != null) {
                int age = calculateAge(birthDate, currentYear);
                yearData.put("age", age);
            } else {
                yearData.put("age", null);
            }

            List<Map<String, Object>> entries = new ArrayList<>();
            
            for (DwdzCrewTimeline timeline : entry.getValue()) {
                // 处理培训记录和履职记录的特殊逻辑
                if ("training".equals(timeline.getEventType())) {
                    String subType = Optional.ofNullable(map.get(timeline.getTimelineId()))
                            .map(DwdzCrewTimeline::getSubType)
                            .orElse(null);
                    entries.addAll(convertTrainingToMultipleEntries(timeline, currentYear,subType));
                } else if ("service".equals(timeline.getEventType())) {
                    entries.addAll(convertServiceToMultipleEntries(timeline, currentYear));
                } else {
                    // 其他类型记录按原逻辑处理
                    Map<String, Object> entryData = convertToTimelineEntryV1(timeline, defectRecordsMap);
                    entries.add(entryData);
                }
            }
            
            yearData.put("entries", entries);

            result.add(yearData);
        }

        // 按年份降序排序
        result.sort((a, b) -> Integer.compare(
                Integer.parseInt((String) b.get("year")),
                Integer.parseInt((String) a.get("year"))));

        return result;
    }

    /**
     * 将时间轴记录转换为V1格式的事件条目
     */
    private Map<String, Object> convertToTimelineEntryV1(DwdzCrewTimeline timeline, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        Map<String, Object> entry = new HashMap<>();
        entry.put("type", timeline.getEventType());
        entry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(timeline.getEventDate()));

        // 根据事件类型添加特定字段
        switch (timeline.getEventType()) {
            case "training":
                convertTrainingEntry(entry, timeline);
                break;
            case "examination":
                convertExaminationEntry(entry, timeline);
                break;
            case "certificate":
                convertCertificateEntry(entry, timeline);
                break;
            case "service":
                convertServiceEntry(entry, timeline);
                break;
            case "medical":
                convertMedicalEntry(entry, timeline);
                break;
            case "entryexit":
                convertEntryExitEntry(entry, timeline);
                break;
            case "supervision":
                convertSupervisionEntry(entry, timeline, defectRecordsMap);
                break;
            // 去掉行业转型
        }

        return entry;
    }

    /**
     * 转换培训记录
     */
    private void convertTrainingEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        entry.put("trainingProject", timeline.getTrainingProject());
        entry.put("trainingInstitution", timeline.getTrainingInstitution());
        entry.put("trainingClassName", timeline.getTrainingClassName());
        entry.put("trainingMarineAuthority", timeline.getTrainingMarineAuthority());
        entry.put("startDate",
                timeline.getTrainingStartDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getTrainingStartDate())
                        : null);
        entry.put("completionDate",
                timeline.getTrainingCompletionDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getTrainingCompletionDate())
                        : null);
        // 翻译培训结果
        entry.put("trainingResult", translateTrainingResult(timeline.getTrainingResult()));
        entry.put("description", timeline.getTrainingDetails());
    }

    /**
     * 转换考试记录
     */
    private void convertExaminationEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        // 判断是报名还是考试
        if (timeline.getExamRegistrationDate() != null) {
            // 考试报名
            entry.put("subType", "registration");
            entry.put("marineAuthority", timeline.getExamMarineAuthority());
            entry.put("examLocation", timeline.getExamLocation());
            entry.put("examPeriod", timeline.getExamPeriod());
            entry.put("marineAuthority", timeline.getExamMarineAuthority());
            entry.put("examSession", timeline.getExamSession());
            entry.put("examPeriod", timeline.getExamPeriod());
            entry.put("position", timeline.getExamPosition());
            entry.put("examSubjects", timeline.getExamSubjects());
            entry.put("registrationDate",
                    new SimpleDateFormat("yyyy-MM-dd").format(timeline.getExamRegistrationDate()));
            entry.put("registrationResult", timeline.getExamRegistrationResult());
        } else {
            // 考试
            entry.put("subType", "exam");
            entry.put("marineAuthority", timeline.getExamMarineAuthority());
            entry.put("examLocation", timeline.getExamLocation());
            entry.put("position", timeline.getExamPosition());
            entry.put("examSubjects", timeline.getExamSubjects());
            entry.put("examPeriod", timeline.getExamPeriod());
            entry.put("examDate",
                    timeline.getExamExamDate() != null
                            ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getExamExamDate())
                            : null);
            entry.put("examResult", timeline.getExamResult());
        }
        entry.put("description", timeline.getExamDetails());
    }

    /**
     * 转换证书申请记录
     */
    private void convertCertificateEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        String certificateName = timeline.getCertApplyCertificateName();
        // 11规则合格证，改成：培训合格证申请
        if (StrUtil.equals(certificateName,"11规则合格证")) {
            certificateName = "培训合格证申请";
        }

        entry.put("subType",timeline.getSubType());
        entry.put("certificateName", certificateName);
        entry.put("certificateNumber", timeline.getCertApplyCertificateNumber());
        entry.put("applicationType", timeline.getCertApplyApplicationType());
        entry.put("applicationResult", timeline.getCertApplyApplicationResult());
        entry.put("certApplyCompName", timeline.getCertApplyCompName());
        entry.put("certApplyFormName", timeline.getCertApplyFormName());
        entry.put("approvalDate",
                timeline.getCertApplyApprovalDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getCertApplyApprovalDate())
                        : null);
        entry.put("marineAuthority", timeline.getCertApplyMarineAuthority());
        entry.put("description", timeline.getCertApplyDetails());
    }

    /**
     * 转换履职记录
     */
    private void convertServiceEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        entry.put("vesselName", timeline.getServiceVesselName());
        entry.put("position", timeline.getServicePosition());
        entry.put("companyName", timeline.getServiceCompanyName());
        entry.put("companyLabel", "公司名称");
        entry.put("vesselType", timeline.getServiceVesselType());
        entry.put("appointmentDate",
                timeline.getServiceAppointmentDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getServiceAppointmentDate())
                        : null);
        entry.put("serviceLocation", timeline.getServiceLocation());
        entry.put("dataSource", timeline.getServiceDataSource());
        entry.put("serviceSource", timeline.getServiceSource());
        entry.put("dspCompanyName", timeline.getServiceDspCompanyName());
        entry.put("dspCompanyId", timeline.getServiceDspCompanyId());
        entry.put("vesselRegno", timeline.getServiceVesselRegno());
        entry.put("vesselImo", timeline.getServiceVesselImo());
        entry.put("vesselId", timeline.getServiceVesselId());
        entry.put("vesselNameEn", timeline.getServiceVesselNameEn());
        entry.put("vesselLevel", timeline.getServiceVesselLevel());
        entry.put("status", timeline.getEndDate() == null ? "current" : "completed");
        entry.put("description", timeline.getServiceDetails());
    }

    /**
     * 转换体检记录
     */
    private void convertMedicalEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        entry.put("medicalHospital", timeline.getMedicalHospital());
        entry.put("applicableDepartment", timeline.getMedicalDepartment());
        entry.put("medicalDate",
                timeline.getMedicalExamDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getMedicalExamDate())
                        : null);
        entry.put("medicalResult", timeline.getMedicalStatus());
        entry.put("medicalMarineAuthority", timeline.getMedicalMarineAuthority());
        entry.put("description", timeline.getMedicalDetails());
    }

    /**
     * 转换出入境记录
     */
    private void convertEntryExitEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        String immigrationDirection = timeline.getImmigrationDirection();
        if (StrUtil.contains(immigrationDirection,"出境")) {
            immigrationDirection = "出境";
        } else if (StrUtil.contains(immigrationDirection, "入境")) {
            immigrationDirection = "入境";
        }
        entry.put("direction", immigrationDirection);
        entry.put("destinationCountry", timeline.getImmigrationDestinationCountry());
        entry.put("borderControl", timeline.getImmigrationBorderControl());
        entry.put("port", timeline.getImmigrationPort());
        entry.put("entryExitTime",
                timeline.getImmigrationEntryExitTime() != null
                        ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(timeline.getImmigrationEntryExitTime())
                        : null);
        entry.put("description", timeline.getImmigrationDetails());
    }

    /**
     * 转换监管记录
     */
    private void convertSupervisionEntry(Map<String, Object> entry, DwdzCrewTimeline timeline, Map<String, List<Map<String, Object>>> defectRecordsMap) {
        String subType = timeline.getSubType();
        entry.put("subType", subType);
        if (StrUtil.isBlank(subType)) {
            return;
        }

        switch (subType) {
            case "award":
                // 奖励
                entry.put("awardAuthority", timeline.getSupervisionAuthority());
                entry.put("awardReason", timeline.getSupervisionAwardReason());
                entry.put("awardContent", timeline.getSupervisionAwardContent());
                entry.put("awardDate",
                        timeline.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getSupervisionProcessingDate())
                                : null);
                break;

            case "violation":
                // 违法记分
                entry.put("inspectionAuthority", timeline.getSupervisionAuthority());
                entry.put("inspectionScorePoints", timeline.getSupervisionScorePoints());
                entry.put("inspectionDate",
                        timeline.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getSupervisionProcessingDate())
                                : null);
                break;

            case "penalty":
                // 行政处罚
                entry.put("caseNumber", timeline.getSupervisionCaseNumber());
                entry.put("caseReason", ""); // 未找到对应字段
                entry.put("incidentDate",
                        timeline.getSupervisionIncidentDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getSupervisionIncidentDate())
                                : null);
                entry.put("incidentLocation", timeline.getSupervisionIncidentLocation());
                entry.put("violationLocation", timeline.getSupervisionViolationLocation());
                entry.put("penaltyDecision", timeline.getSupervisionPenaltyDecision());
                entry.put("certificateDetained", timeline.getSupervisionCertificateDetain());
                break;

            case "inspection":
            case "fsc":
            case "psc":
                // 现场安全监督检查和船舶安检
                entry.put("inspectionAuthority", timeline.getSupervisionAuthority());
                entry.put("vesselName", timeline.getSupervisionVesselName());
                entry.put("inspectionDate",
                        timeline.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getSupervisionProcessingDate())
                                : null);
                entry.put("inspectNo", timeline.getSupervisionInspectNo());
                entry.put("initCode", timeline.getSupervisionInitCode());
                
                // 从批量查询结果中获取缺陷详情
                List<Map<String, Object>> defectRecords = new ArrayList<>();
                if (StrUtil.isNotBlank(timeline.getSupervisionInspectNo())) {
                    defectRecords = defectRecordsMap.get(timeline.getSupervisionInspectNo());
                    if (defectRecords == null) {
                        defectRecords = new ArrayList<>();
                    }
                }
                entry.put("defectRecords", defectRecords);
                
                // 根据缺陷记录数量设置defectDetails
                if (defectRecords.isEmpty()) {
                    entry.put("defectDetails", "零缺陷");
                } else {
                    entry.put("defectDetails", String.valueOf(defectRecords.size()));
                }
                
                // 添加初查/复查显示
                String initCode = timeline.getSupervisionInitCode();
                if (StrUtil.isNotBlank(initCode)) {
                    if ("0".equals(initCode)) {
                        entry.put("inspectionType", "初查");
                    } else if ("1".equals(initCode)) {
                        entry.put("inspectionType", "复查");
                    } else {
                        entry.put("inspectionType", "监管");
                    }
                } else {
                    entry.put("inspectionType", "监管");
                }
                break;

            default:
                // 默认监管记录
                entry.put("inspectionAuthority", timeline.getSupervisionAuthority());
                entry.put("vesselName", timeline.getSupervisionVesselName());
                entry.put("inspectionDate",
                        timeline.getSupervisionProcessingDate() != null
                                ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getSupervisionProcessingDate())
                                : null);
                entry.put("inspectNo", timeline.getSupervisionInspectNo());
                entry.put("initCode", timeline.getSupervisionInitCode());
                
                // 从批量查询结果中获取缺陷详情
                List<Map<String, Object>> defectRecordsDefault = new ArrayList<>();
                if (StrUtil.isNotBlank(timeline.getSupervisionInspectNo())) {
                    defectRecordsDefault = defectRecordsMap.get(timeline.getSupervisionInspectNo());
                    if (defectRecordsDefault == null) {
                        defectRecordsDefault = new ArrayList<>();
                    }
                }
                entry.put("defectRecords", defectRecordsDefault);
                
                // 根据缺陷记录数量设置defectDetails
                if (defectRecordsDefault.isEmpty()) {
                    entry.put("defectDetails", "零缺陷");
                } else {
                    entry.put("defectDetails", String.valueOf(defectRecordsDefault.size()));
                }
                
                // 添加初查/复查显示
                String initCodeDefault = timeline.getSupervisionInitCode();
                if (StrUtil.isNotBlank(initCodeDefault)) {
                    if ("0".equals(initCodeDefault)) {
                        entry.put("inspectionType", "初查");
                    } else if ("1".equals(initCodeDefault)) {
                        entry.put("inspectionType", "复查");
                    } else {
                        entry.put("inspectionType", "监管");
                    }
                } else {
                    entry.put("inspectionType", "监管");
                }
                break;
        }

        // 通用字段
        entry.put("subType", timeline.getSubType());
    }

    /**
     * 转换行业转型记录
     */
    private void convertTransitionEntry(Map<String, Object> entry, DwdzCrewTimeline timeline) {
        entry.put("company", timeline.getCareerChangeCompany());
        entry.put("transitionIdentity", timeline.getCareerChangeNewIdentity());
        entry.put("transitionDate",
                timeline.getCareerChangeEffectiveDate() != null
                        ? new SimpleDateFormat("yyyy-MM-dd").format(timeline.getCareerChangeEffectiveDate())
                        : null);
        entry.put("status", "completed");
        entry.put("description", timeline.getCareerChangeDetails());
    }



    /**
     * 根据日期范围过滤履职记录
     */
    private List<DwdzCrewTimeline> filterServiceRecordsByDateRange(List<DwdzCrewTimeline> serviceRecords,
            String dateStart, String dateEnd) {
        if (serviceRecords == null || serviceRecords.isEmpty()) {
            return serviceRecords;
        }

        List<DwdzCrewTimeline> filteredRecords = new ArrayList<>();

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = StrUtil.isNotBlank(dateStart) ? sdf.parse(dateStart) : null;
            Date endDate = StrUtil.isNotBlank(dateEnd) ? sdf.parse(dateEnd) : null;

            for (DwdzCrewTimeline record : serviceRecords) {
                Date effectiveStartDate = getEffectiveStartDate(record);
                Date effectiveEndDate = record.getEndDate();

                // 如果履职记录在日期范围内，则保留
                if (isDateInRange(effectiveStartDate, effectiveEndDate, startDate, endDate)) {
                    filteredRecords.add(record);
                }
            }
        } catch (Exception e) {
            log.error("过滤履职记录时发生异常", e);
            return serviceRecords; // 发生异常时返回原记录
        }

        return filteredRecords;
    }

    /**
     * 根据日期范围过滤动态记录
     */
    private List<DwdzCrewTimeline> filterDynamicRecordsByDateRange(List<DwdzCrewTimeline> dynamicRecords,
            String dateStart, String dateEnd) {
        if (dynamicRecords == null || dynamicRecords.isEmpty()) {
            return dynamicRecords;
        }

        List<DwdzCrewTimeline> filteredRecords = new ArrayList<>();

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = StrUtil.isNotBlank(dateStart) ? sdf.parse(dateStart) : null;
            Date endDate = StrUtil.isNotBlank(dateEnd) ? sdf.parse(dateEnd) : null;

            for (DwdzCrewTimeline record : dynamicRecords) {
                Date dynamicDate = getDynamicEffectiveDate(record);

                // 如果动态记录在日期范围内，则保留
                if (isDateInRange(dynamicDate, dynamicDate, startDate, endDate)) {
                    filteredRecords.add(record);
                }
            }
        } catch (Exception e) {
            log.error("过滤动态记录时发生异常", e);
            return dynamicRecords; // 发生异常时返回原记录
        }

        return filteredRecords;
    }

    /**
     * 翻译培训结果
     * 
     * @param trainingResult 英文培训结果
     * @return 中文培训结果
     */
    private String translateTrainingResult(String trainingResult) {
        if (trainingResult == null || trainingResult.trim().isEmpty()) {
            return trainingResult;
        }

        // 转换为小写进行匹配
        String lowerResult = trainingResult.toLowerCase().trim();
        String translatedResult = trainingResultMap.get(lowerResult);

        // 如果找到翻译，返回中文结果；否则返回原值
        return translatedResult != null ? translatedResult : trainingResult;
    }

    /**
     * 计算指定年份的年龄
     * 
     * @param birthDate 出生日期
     * @param targetYear 目标年份
     * @return 年龄
     */
    private int calculateAge(Date birthDate, int targetYear) {
        if (birthDate == null) {
            return 0;
        }
        
        Calendar birthCal = Calendar.getInstance();
        birthCal.setTime(birthDate);
        int birthYear = birthCal.get(Calendar.YEAR);
        
        return targetYear - birthYear;
    }

    /**
     * 将培训记录转换为多条时间轴条目
     * 
     * @param timeline 培训记录
     * @param currentYear 当前年份
     * @param type 培训子类型
     * @return 时间轴条目列表
     */
    private List<Map<String, Object>> convertTrainingToMultipleEntries(DwdzCrewTimeline timeline, int currentYear,String type) {
        List<Map<String, Object>> entries = new ArrayList<>();

        // 添加调试日志
        log.info("处理培训记录，船员ID：{}，培训项目：{}，开始日期：{}，结束日期：{}，当前年份：{}", 
                timeline.getCrewId(), timeline.getTrainingProject(), 
                timeline.getTrainingStartDate(), timeline.getTrainingCompletionDate(), currentYear);

        // 获取培训开始日期
        Date trainingStartDate = timeline.getTrainingStartDate();
        
        // 获取培训结束日期 - 优先使用trainingCompletionDate，如果为空则使用trainingEndDate
        Date trainingCompletionDate = timeline.getTrainingCompletionDate();
        if (trainingCompletionDate == null) {
            trainingCompletionDate = timeline.getTrainingEndDate();
        }
        
        // 检查是否为跨年度培训
        boolean isCrossYear = false;
        if (trainingStartDate != null && trainingCompletionDate != null) {
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(trainingStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(trainingCompletionDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            isCrossYear = endYear > startYear;
        }
        
        // 根据subType标记决定显示开始还是结束记录
        String subType = timeline.getSubType();
        if ("start".equals(subType)) {
            // 显示培训开始记录
            Map<String, Object> startEntry = new HashMap<>();
            startEntry.put("type", "training");
            startEntry.put("subType", "start");
            startEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
            startEntry.put("title", timeline.getTrainingProject() + " - 培训开始");
            startEntry.put("status", "开始培训");

            // 添加培训相关字段
            startEntry.put("trainingProject", timeline.getTrainingProject());
            startEntry.put("trainingInstitution", timeline.getTrainingInstitution());
            startEntry.put("trainingClassName", timeline.getTrainingClassName());
            startEntry.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
            startEntry.put("description", timeline.getTrainingDetails());
            // 所属辖区
            startEntry.put("trainingMarineAuthority", timeline.getTrainingMarineAuthority());
            
            // 添加跨年度标记
            if (isCrossYear) {
                startEntry.put("crossYear", true);
                startEntry.put("crossYearMark", "跨年度");
            } else {
                startEntry.put("crossYear", false);
                startEntry.put("crossYearMark", "");
            }

            if (StrUtil.equals(type,"academic")) {
                startEntry.put("busiType","academic");
            }

            entries.add(startEntry);
        } else if ("end".equals(subType)) {
            // 显示培训结束记录
            Map<String, Object> endEntry = new HashMap<>();
            endEntry.put("type", "training");
            endEntry.put("subType", "completion");
            endEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(trainingCompletionDate));

            // 根据培训结果设置标题
            String result = timeline.getTrainingResult();
            String translatedResult = translateTrainingResult(result);
            String title = timeline.getTrainingProject() + " - " + translatedResult;
            endEntry.put("title", title);
            endEntry.put("status", translatedResult);

            // 添加培训相关字段
            endEntry.put("trainingProject", timeline.getTrainingProject());
            endEntry.put("trainingInstitution", timeline.getTrainingInstitution());
            endEntry.put("trainingClassName", timeline.getTrainingClassName());
            endEntry.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
            endEntry.put("completionDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingCompletionDate));
            endEntry.put("trainingResult", translatedResult);
            endEntry.put("description", timeline.getTrainingDetails());
            // 所属辖区
            endEntry.put("trainingMarineAuthority", timeline.getTrainingMarineAuthority());
            
            // 添加跨年度标记
            if (isCrossYear) {
                endEntry.put("crossYear", true);
                endEntry.put("crossYearMark", "跨年度");
            } else {
                endEntry.put("crossYear", false);
                endEntry.put("crossYearMark", "");
            }

            if (StrUtil.equals(type,"academic")) {
                endEntry.put("busiType","academic");
            }

            entries.add(endEntry);
        } else {
            // 如果没有subType标记，按原来的逻辑处理（兼容性）
            // 情况1：培训已完成（trainingStartDate 和 trainingCompletionDate 都有值）
            if (trainingStartDate != null && trainingCompletionDate != null) {
                log.info("培训已完成，开始日期：{}，结束日期：{}", trainingStartDate, trainingCompletionDate);
                
                // 检查培训开始日期是否在当前年份
                Calendar startCal = Calendar.getInstance();
                startCal.setTime(trainingStartDate);
                int startYear = startCal.get(Calendar.YEAR);

                if (startYear == currentYear) {
                    log.info("培训开始日期在当前年份，添加培训开始记录");
                    // 添加培训开始记录
                    Map<String, Object> startEntry = new HashMap<>();
                    startEntry.put("type", "training");
                    startEntry.put("subType", "start");
                    startEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
                    startEntry.put("title", timeline.getTrainingProject() + " - 培训开始");
                    startEntry.put("status", "培训中");

                    // 添加培训相关字段
                    startEntry.put("trainingProject", timeline.getTrainingProject());
                    startEntry.put("trainingInstitution", timeline.getTrainingInstitution());
                    startEntry.put("trainingClassName", timeline.getTrainingClassName());
                    startEntry.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
                    startEntry.put("description", timeline.getTrainingDetails());
                    // 所属辖区
                    startEntry.put("trainingMarineAuthority", timeline.getTrainingMarineAuthority());
                    
                    // 添加跨年度标记
                    if (isCrossYear) {
                        startEntry.put("crossYear", true);
                        startEntry.put("crossYearMark", "跨年度");
                    } else {
                        startEntry.put("crossYear", false);
                        startEntry.put("crossYearMark", "");
                    }

                    if (StrUtil.equals(type,"academic")) {
                        startEntry.put("busiType","academic");
                    }

                    entries.add(startEntry);
                }

                // 检查培训结束日期是否在当前年份
                Calendar endCal = Calendar.getInstance();
                endCal.setTime(trainingCompletionDate);
                int endYear = endCal.get(Calendar.YEAR);

                if (endYear == currentYear) {
                    log.info("培训结束日期在当前年份，添加培训结束记录");
                    // 添加培训结束记录
                    Map<String, Object> endEntry = new HashMap<>();
                    endEntry.put("type", "training");
                    endEntry.put("subType", "completion");
                    endEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(trainingCompletionDate));

                    // 根据培训结果设置标题
                    String result = timeline.getTrainingResult();
                    String translatedResult = translateTrainingResult(result);
                    String title = timeline.getTrainingProject() + " - " + translatedResult;
                    endEntry.put("title", title);
                    endEntry.put("status", translatedResult);

                    // 添加培训相关字段
                    endEntry.put("trainingProject", timeline.getTrainingProject());
                    endEntry.put("trainingInstitution", timeline.getTrainingInstitution());
                    endEntry.put("trainingClassName", timeline.getTrainingClassName());
                    endEntry.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingStartDate));
                    endEntry.put("completionDate", new SimpleDateFormat("yyyy-MM-dd").format(trainingCompletionDate));
                    endEntry.put("trainingResult", translatedResult);
                    endEntry.put("description", timeline.getTrainingDetails());
                    // 所属辖区
                    endEntry.put("trainingMarineAuthority", timeline.getTrainingMarineAuthority());
                    
                    // 添加跨年度标记
                    if (isCrossYear) {
                        endEntry.put("crossYear", true);
                        endEntry.put("crossYearMark", "跨年度");
                    } else {
                        endEntry.put("crossYear", false);
                        endEntry.put("crossYearMark", "");
                    }

                    if (StrUtil.equals(type,"academic")) {
                        endEntry.put("busiType","academic");
                    }

                    entries.add(endEntry);
                }
            }
        }
        
        return entries;
    }

    /**
     * 将履职记录转换为多条时间轴条目
     * 
     * @param timeline 履职记录
     * @param currentYear 当前年份
     * @return 时间轴条目列表
     */
    private List<Map<String, Object>> convertServiceToMultipleEntries(DwdzCrewTimeline timeline, int currentYear) {
        List<Map<String, Object>> entries = new ArrayList<>();
        
        // 获取履职开始日期（优先使用 serviceDate）
        Date serviceStartDate = timeline.getServiceDate();
        if (serviceStartDate == null) {
            serviceStartDate = getEffectiveStartDate(timeline);
        }
        
        // 获取履职结束日期（优先使用 serviceEndDate）
        Date serviceEndDate = timeline.getServiceEndDate();
        if (serviceEndDate == null) {
            serviceEndDate = timeline.getEndDate();
        }
        
        // 检查是否为跨年度履职
        boolean isCrossYear = false;
        if (serviceStartDate != null && serviceEndDate != null) {
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(serviceStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(serviceEndDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            isCrossYear = endYear > startYear;
        }
        
        // 根据subType标记决定显示开始还是结束记录
        String subType = timeline.getSubType();
        if ("start".equals(subType)) {
            // 显示履职开始记录
            Map<String, Object> startEntry = new HashMap<>();
            startEntry.put("type", "service");
            startEntry.put("subType", "appointment");
            startEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
            startEntry.put("title", timeline.getServicePosition() + " - 履职开始");
            startEntry.put("status", "任职");
            
            // 添加履职相关字段
            startEntry.put("vesselName", timeline.getServiceVesselName());
            startEntry.put("position", timeline.getServicePosition());
            startEntry.put("companyName", timeline.getServiceCompanyName());
            startEntry.put("vesselType", timeline.getServiceVesselType());
            startEntry.put("appointmentDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
            startEntry.put("serviceLocation", timeline.getServiceLocation());
            startEntry.put("dataSource", timeline.getServiceDataSource());
            startEntry.put("description", timeline.getServiceDetails());
            startEntry.put("vesselId", timeline.getServiceVesselId());
            startEntry.put("vesselImo", timeline.getServiceVesselImo());
            // 派遣报备，展示：航运公司、外派公司；--展示两个公司名称
            // 中国籍船舶国际航行报备：服务机构、航运公司；--展示两个公司名称
            if (StrUtil.equalsAny(timeline.getServiceDataSource(),"派遣报备","中国籍船舶国际航行报备")) {
                startEntry.put("companyName", timeline.getServiceCompanyName());
                startEntry.put("dspCompanyName", timeline.getServiceDspCompanyName());
            }

            // 添加跨年度标记
            if (isCrossYear) {
                startEntry.put("crossYear", true);
                startEntry.put("crossYearMark", "跨年度");
            } else {
                startEntry.put("crossYear", false);
                startEntry.put("crossYearMark", "");
            }
            
            entries.add(startEntry);
        } else if ("end".equals(subType)) {
            // 显示履职结束记录
            Map<String, Object> endEntry = new HashMap<>();
            endEntry.put("type", "service");
            endEntry.put("subType", "termination");
            endEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
            endEntry.put("title", timeline.getServicePosition() + " - 履职结束");
            endEntry.put("status", "解职");
            
            // 添加履职相关字段
            endEntry.put("vesselName", timeline.getServiceVesselName());
            endEntry.put("position", timeline.getServicePosition());
            endEntry.put("companyName", timeline.getServiceCompanyName());
            endEntry.put("vesselType", timeline.getServiceVesselType());
            endEntry.put("endDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
            endEntry.put("terminationDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
            endEntry.put("serviceEndLocation", timeline.getServiceEndLocation());
            endEntry.put("terminationLocation", timeline.getServiceEndLocation());
            endEntry.put("dataSource", timeline.getServiceDataSource());
            endEntry.put("description", timeline.getServiceDetails());
            endEntry.put("vesselId", timeline.getServiceVesselId());
            endEntry.put("vesselImo", timeline.getServiceVesselImo());

            // 派遣报备，展示：航运公司、外派公司；--展示两个公司名称
            // 中国籍船舶国际航行报备：服务机构、航运公司；--展示两个公司名称
            if (StrUtil.equalsAny(timeline.getServiceDataSource(),"派遣报备","中国籍船舶国际航行报备")) {
                endEntry.put("companyName", timeline.getServiceCompanyName());
                endEntry.put("dspCompanyName", timeline.getServiceDspCompanyName());
            }
            
            // 添加跨年度标记
            if (isCrossYear) {
                endEntry.put("crossYear", true);
                endEntry.put("crossYearMark", "跨年度");
            } else {
                endEntry.put("crossYear", false);
                endEntry.put("crossYearMark", "");
            }
            
            entries.add(endEntry);
        } else {
            // 如果没有subType标记，按原来的逻辑处理（兼容性）
            // 情况1：履职已完成（serviceDate 和 serviceEndDate 都有值）
            if (serviceStartDate != null && serviceEndDate != null) {
                // 检查履职开始日期是否在当前年份
                Calendar startCal = Calendar.getInstance();
                startCal.setTime(serviceStartDate);
                int startYear = startCal.get(Calendar.YEAR);
                
                if (startYear == currentYear) {
                    // 添加履职开始记录
                    Map<String, Object> startEntry = new HashMap<>();
                    startEntry.put("type", "service");
                    startEntry.put("subType", "appointment");
                    startEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
                    startEntry.put("title", timeline.getServicePosition() + " - 履职开始");
                    startEntry.put("status", "任职中");
                    
                    // 添加履职相关字段
                    startEntry.put("vesselName", timeline.getServiceVesselName());
                    startEntry.put("position", timeline.getServicePosition());
                    startEntry.put("companyName", timeline.getServiceCompanyName());
                    startEntry.put("vesselType", timeline.getServiceVesselType());
                    startEntry.put("appointmentDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
                    startEntry.put("serviceLocation", timeline.getServiceLocation());
                    startEntry.put("dataSource", timeline.getServiceDataSource());
                    startEntry.put("description", timeline.getServiceDetails());
                    
                    // 添加跨年度标记
                    if (isCrossYear) {
                        startEntry.put("crossYear", true);
                        startEntry.put("crossYearMark", "跨年度");
                    } else {
                        startEntry.put("crossYear", false);
                        startEntry.put("crossYearMark", "");
                    }
                    
                    entries.add(startEntry);
                }
                
                // 检查履职结束日期是否在当前年份
                Calendar endCal = Calendar.getInstance();
                endCal.setTime(serviceEndDate);
                int endYear = endCal.get(Calendar.YEAR);
                
                if (endYear == currentYear) {
                    // 添加履职结束记录
                    Map<String, Object> endEntry = new HashMap<>();
                    endEntry.put("type", "service");
                    endEntry.put("subType", "termination");
                    endEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                    endEntry.put("title", timeline.getServicePosition() + " - 履职结束");
                    endEntry.put("status", "已解职");
                    
                    // 添加履职相关字段
                    endEntry.put("vesselName", timeline.getServiceVesselName());
                    endEntry.put("position", timeline.getServicePosition());
                    endEntry.put("companyName", timeline.getServiceCompanyName());
                    endEntry.put("vesselType", timeline.getServiceVesselType());
                    endEntry.put("endDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                    endEntry.put("terminationDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                    endEntry.put("serviceEndLocation", timeline.getServiceEndLocation());
                    endEntry.put("terminationLocation", timeline.getServiceEndLocation());
                    endEntry.put("dataSource", timeline.getServiceDataSource());
                    endEntry.put("description", timeline.getServiceDetails());
                    
                    // 添加跨年度标记
                    if (isCrossYear) {
                        endEntry.put("crossYear", true);
                        endEntry.put("crossYearMark", "跨年度");
                    } else {
                        endEntry.put("crossYear", false);
                        endEntry.put("crossYearMark", "");
                    }
                    
                    entries.add(endEntry);
                }
            }
        }
        
        return entries;
    }

    /**
     * 判断日期是否在指定范围内
     */
    private boolean isDateInRange(Date targetStartDate, Date targetEndDate, Date rangeStartDate, Date rangeEndDate) {
        // 如果没有指定范围，则返回true
        if (rangeStartDate == null && rangeEndDate == null) {
            return true;
        }

        // 如果目标日期为空，则返回false
        if (targetStartDate == null) {
            return false;
        }

        // 检查开始日期
        if (rangeStartDate != null && targetStartDate.before(rangeStartDate)) {
            return false;
        }

        // 检查结束日期
        if (rangeEndDate != null) {
            Date normalizedEnd = normalizeEndDate(targetEndDate);
            Date effectiveEndDate = normalizedEnd != null ? normalizedEnd : targetStartDate;
            if (effectiveEndDate.after(rangeEndDate)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 业务规则：解职年份为1900或为空 => 视为至今（进行中）
     */
    private Date normalizeEndDate(Date endDate) {
        if (endDate == null) return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(endDate);
        if (cal.get(Calendar.YEAR) <= 1900) {
            return null;
        }
        return endDate;
    }

    private boolean isOngoing(Date normalizedEnd) {
        return normalizedEnd == null;
    }

    private String formatEndDateUntilNow(Date normalizedEnd) {
        if (normalizedEnd == null) {
            return "至今";
        }
        return new java.text.SimpleDateFormat("yyyy-MM-dd").format(normalizedEnd);
    }

    /**
     * 生成记录的唯一标识符，用于去重
     */
    private String generateRecordKey(DwdzCrewTimeline timeline) {
        StringBuilder key = new StringBuilder();
        key.append(timeline.getEventType()).append("_");
        
        if ("training".equals(timeline.getEventType())) {
            key.append(timeline.getTrainingStartDate()).append("_");
            key.append(timeline.getTrainingCompletionDate()).append("_");
            key.append(timeline.getTrainingProject()).append("_");
            key.append(timeline.getTrainingInstitution());
        } else if ("service".equals(timeline.getEventType())) {
            key.append(timeline.getServiceDate()).append("_");
            key.append(timeline.getServiceEndDate()).append("_");
            key.append(timeline.getServiceVesselName()).append("_");
            key.append(timeline.getServicePosition()).append("_");
            key.append(timeline.getServiceCompanyName());
        }
        
        return key.toString();
    }

    /**
     * 获取跨年度履职记录
     */
    private List<Map<String, Object>> getCrossYearServiceEntries(List<DwdzCrewTimeline> timelineList, int currentYear) {
        List<Map<String, Object>> entries = new ArrayList<>();
        
        for (DwdzCrewTimeline timeline : timelineList) {
            if (!"service".equals(timeline.getEventType())) {
                continue;
            }
            
            // 获取履职开始和结束日期
            Date serviceStartDate = timeline.getServiceDate();
            if (serviceStartDate == null) {
                serviceStartDate = getEffectiveStartDate(timeline);
            }
            
            Date serviceEndDate = timeline.getServiceEndDate();
            if (serviceEndDate == null) {
                serviceEndDate = timeline.getEndDate();
            }
            
            if (serviceStartDate == null || serviceEndDate == null) {
                continue;
            }
            
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(serviceStartDate);
            int startYear = startCal.get(Calendar.YEAR);

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(serviceEndDate);
            int endYear = endCal.get(Calendar.YEAR);
            
            // 检查是否为跨年度履职
            boolean isCrossYear = endYear > startYear;
            
            // 如果当前年份是开始年份，添加履职开始记录
            if (startYear == currentYear) {
                Map<String, Object> startEntry = new HashMap<>();
                startEntry.put("type", "service");
                startEntry.put("subType", "appointment");
                startEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
                startEntry.put("title", timeline.getServicePosition() + " - 履职开始");
                startEntry.put("status", "任职中");
                startEntry.put("vesselName", timeline.getServiceVesselName());
                startEntry.put("position", timeline.getServicePosition());
                startEntry.put("companyName", timeline.getServiceCompanyName());
                startEntry.put("vesselType", timeline.getServiceVesselType());
                startEntry.put("appointmentDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceStartDate));
                startEntry.put("serviceLocation", timeline.getServiceLocation());
                startEntry.put("dataSource", timeline.getServiceDataSource());
                startEntry.put("description", timeline.getServiceDetails());
                startEntry.put("crossYear", true);
                startEntry.put("crossYearMark", "跨年度");
                
                entries.add(startEntry);
            }
            
            // 如果当前年份是结束年份，添加履职结束记录
            if (endYear == currentYear) {
                Map<String, Object> endEntry = new HashMap<>();
                endEntry.put("type", "service");
                endEntry.put("subType", "termination");
                endEntry.put("date", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                endEntry.put("title", timeline.getServicePosition() + " - 履职结束");
                endEntry.put("status", "已解职");
                endEntry.put("vesselName", timeline.getServiceVesselName());
                endEntry.put("position", timeline.getServicePosition());
                endEntry.put("companyName", timeline.getServiceCompanyName());
                endEntry.put("vesselType", timeline.getServiceVesselType());
                endEntry.put("endDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                endEntry.put("terminationDate", new SimpleDateFormat("yyyy-MM-dd").format(serviceEndDate));
                endEntry.put("serviceEndLocation", timeline.getServiceEndLocation());
                endEntry.put("terminationLocation", timeline.getServiceEndLocation());
                endEntry.put("dataSource", timeline.getServiceDataSource());
                endEntry.put("description", timeline.getServiceDetails());
                endEntry.put("crossYear", true);
                endEntry.put("crossYearMark", "跨年度");
                
                entries.add(endEntry);
            }
        }
        
        return entries;
    }

    /**
     * 生成履职记录的唯一标识符
     */
    private String generateServiceTimelineKey(DwdzCrewTimeline timeline) {
        StringBuilder key = new StringBuilder();
        key.append(timeline.getServiceDate()).append("_");
        key.append(timeline.getServiceEndDate()).append("_");
        key.append(timeline.getServiceVesselName()).append("_");
        key.append(timeline.getServicePosition()).append("_");
        key.append(timeline.getServiceCompanyName());
        return key.toString();
    }

    /**
     * 生成履职条目的唯一标识符
     */
    private String generateServiceEntryKey(Map<String, Object> entry) {
        StringBuilder key = new StringBuilder();
        key.append(entry.get("date")).append("_");
        key.append(entry.get("vesselName")).append("_");
        key.append(entry.get("position")).append("_");
        key.append(entry.get("companyName"));
        return key.toString();
    }
}